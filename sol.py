#!/usr/bin/env python3
from sympy import symbols, Poly, roots, Integer
from sympy.ntheory import isprime
from Crypto.Util.number import long_to_bytes
import re, binascii

# ---------- challenge data ----------
h1 = 44626154099651354925697068610752642661842459492769931945027538340211738148995902544351457443643808803963130274930824732652561687395268828472477422919262224
h2 = 516671113554555861164166966331322883848052630063409185414998284127910160310316421085219788291486248715029393774584960034375836715001130337767354512063372620828300201147366138270597133744747341658011663632381219284289144790858167258162656417236910634201286428763727072739569460623482985066956478781223378673732
h3 = 6147718474663450187001867904227777991349731066494841442199681943204194617136760567222545181562592364728655444222576167723225771866335920325045525027985716792468801076590684892140052786942251780392395274059384743594570343510311801194684613435002073956759521242578078411431891501758484581445964234548107005826532945720412531638919892681259687552977883437895032963223761216846303917338652743754915155934118353066174102436448393348040719582422022713292561416343278608
N  = 14184841414933523698606245433393907034474143715949896731683874356940146602876788990832087413915033843120975580859113356518777762025417525571528638829956003882418585702756644491932279294535883798799580861254646149745925137179207140600356428758736111639677698862407787386573263961111978517446397007747429416079059195916290615125084899002162504424765939524455434579218079962808920072946861658695379491917567048202142417165204141307476222251547098848515065051745905180788313450494477967398727631152936238366581978379130450660235139256967936160718128731512409111209840405772933034600016694225294481603355934917366484109057
ct = 7206073305613702374599111614814906970440294727803485526309240639632267579843683565802173379827833956201159574420824719776147819102099336962514796156896676759583546811968236522994354575329441893002238163033156253024723024949055759106002778923759513660310612191734651556865862062466610096121560946958417413090025085357645113435690155185872476007965208478560113777772287491829589470150297314561174045606263477749855072753028828654003150451735015590824316724902277285805923797405082147262496358
e  = 65537
# -------------------------------------

# Convert to SymPy Integer for exact arithmetic
S1, S2, S3, S4 = map(Integer, (h1, h2, h3, N))

# <PERSON>’s identities for 4 variables
e1 = S1
e2 = (S1**2 - S2) // 2
e3 = (S1**3 - 3*S1*S2 + 2*S3) // 6
e4 = S4   # = p*q*r*s

# Build the monic polynomial with roots p,q,r,s
x = symbols('x')
poly = Poly(x**4 - e1*x**3 + e2*x**2 - e3*x + e4, x)

# Factor / find integer roots
root_dict = roots(poly)          # {root: multiplicity}
primes = [Integer(r) for r in root_dict.keys()]

# Sanity checks
assert len(primes) == 4, "Did not get four distinct roots"
assert all(isprime(p) for p in primes), "Some roots are not prime"
assert int(primes[0] * primes[1] * primes[2] * primes[3]) == int(N), "Roots do not multiply to N"

# Compute φ(N) = Π (pi - 1)
phi = 1
for p in primes:
    phi *= (p - 1)

# Private exponent
d = pow(e, -1, int(phi))

# Decrypt
pt    = pow(ct, d, int(N))
plain = long_to_bytes(pt)

# Try to print nicely
try:
    printable = plain.decode()
    # If it contains the expected flag format, show only that
    m = re.search(r'uiuctf\{.*?\}', printable)
    if m:
        print("🎯 Flag:", m.group())
    else:
        print("✅ Decrypted (UTF‑8):", printable)
except UnicodeDecodeError:
    print("⚠️ Flag not valid UTF‑8, raw bytes/hex shown below")
    print("raw bytes:", plain)
    print("hex:", binascii.hexlify(plain).decode())
